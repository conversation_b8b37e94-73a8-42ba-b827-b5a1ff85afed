<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EX跨境支付平台 - 中非经贸博览会专业展示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: #333;
            overflow-x: hidden;
        }

        .slide-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .slide {
            background: white;
            margin: 40px 0;
            border-radius: 20px;
            padding: 50px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            min-height: 80vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .slide::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }

        .slide-header {
            text-align: center;
            margin-bottom: 50px;
        }

        .slide-title {
            font-size: 42px;
            color: #1e3c72;
            margin-bottom: 15px;
            font-weight: 700;
        }

        .slide-subtitle {
            font-size: 20px;
            color: #666;
            font-weight: 300;
        }

        .hero-slide {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            justify-content: center;
        }

        .hero-slide .slide-title {
            font-size: 56px;
            color: white;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .hero-slide .slide-subtitle {
            font-size: 24px;
            color: rgba(255,255,255,0.9);
            margin-bottom: 30px;
        }

        .hero-tagline {
            font-size: 20px;
            background: rgba(255,255,255,0.2);
            padding: 15px 40px;
            border-radius: 30px;
            display: inline-block;
            margin-bottom: 40px;
        }

        .company-logos {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 40px;
            margin-top: 30px;
        }

        .logo-item {
            text-align: center;
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .logo-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .logo-desc {
            font-size: 14px;
            opacity: 0.8;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 40px;
            margin: 40px 0;
        }

        .content-grid.three-col {
            grid-template-columns: repeat(3, 1fr);
        }

        .content-grid.four-col {
            grid-template-columns: repeat(4, 1fr);
        }

        .card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 30px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
            transition: all 0.3s ease;
            height: 100%;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }

        .card-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }

        .card-title {
            font-size: 22px;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 15px;
        }

        .card-content {
            font-size: 16px;
            color: #666;
            line-height: 1.6;
        }

        .highlight-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .highlight-card .card-title {
            color: white;
        }

        .highlight-card .card-content {
            color: rgba(255,255,255,0.9);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 30px;
            margin: 40px 0;
        }

        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: scale(1.05);
        }

        .stat-number {
            font-size: 42px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .stat-label {
            font-size: 16px;
            opacity: 0.9;
        }

        .africa-regions {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin: 40px 0;
        }

        .region-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            border-left: 6px solid;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }

        .region-card:hover {
            transform: translateY(-5px);
        }

        .region-card.east { border-left-color: #4CAF50; }
        .region-card.west { border-left-color: #2196F3; }
        .region-card.south { border-left-color: #FF9800; }
        .region-card.north { border-left-color: #F44336; }

        .region-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #1e3c72;
        }

        .region-stats {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
        }

        .region-stat {
            text-align: center;
            flex: 1;
        }

        .region-stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .region-stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .region-info {
            font-size: 16px;
            color: #666;
            line-height: 1.6;
        }

        .tech-showcase {
            background: #f8f9fa;
            padding: 40px;
            border-radius: 20px;
            margin: 40px 0;
        }

        .tech-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 30px;
        }

        .tech-item {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .tech-item:hover {
            transform: translateY(-5px);
        }

        .tech-icon {
            font-size: 40px;
            margin-bottom: 15px;
            color: #667eea;
        }

        .tech-title {
            font-size: 18px;
            font-weight: bold;
            color: #1e3c72;
            margin-bottom: 10px;
        }

        .tech-desc {
            font-size: 14px;
            color: #666;
        }

        .contact-slide {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            text-align: center;
        }

        .contact-slide .slide-title {
            color: white;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 40px;
            margin-top: 40px;
        }

        .contact-item {
            background: rgba(255,255,255,0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }

        .contact-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .contact-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .contact-info {
            font-size: 16px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .content-grid,
            .africa-regions,
            .stats-grid,
            .tech-grid,
            .contact-grid {
                grid-template-columns: 1fr;
            }

            .slide {
                padding: 30px;
                min-height: auto;
            }

            .slide-title {
                font-size: 32px;
            }

            .hero-slide .slide-title {
                font-size: 42px;
            }
        }

        .fade-in {
            animation: fadeIn 1s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="slide-container">
        <!-- 第1页：封面 -->
        <div class="slide hero-slide fade-in">
            <h1 class="slide-title">EX跨境支付平台</h1>
            <p class="slide-subtitle">中非经贸博览会专业展示</p>
            <div class="hero-tagline">连接中非贸易 · 赋能全球支付</div>

            <div class="company-logos">
                <div class="logo-item">
                    <div class="logo-name">🌟 EUREWAX</div>
                    <div class="logo-desc">科技公司</div>
                </div>
                <div class="logo-item">
                    <div class="logo-name">💳 iPay Links</div>
                    <div class="logo-desc">多年跨境支付经验</div>
                </div>
                <div class="logo-item">
                    <div class="logo-name">🚀 EX平台</div>
                    <div class="logo-desc">科技输出解决方案</div>
                </div>
            </div>
        </div>

        <!-- 第2页：公司介绍 -->
        <div class="slide fade-in">
            <div class="slide-header">
                <h2 class="slide-title">🏢 关于我们</h2>
                <p class="slide-subtitle">科技驱动的跨境支付专家</p>
            </div>

            <div class="content-grid">
                <div class="card highlight-card">
                    <span class="card-icon">🌟</span>
                    <h3 class="card-title">EUREWAX科技公司</h3>
                    <div class="card-content">
                        专注于金融科技创新的领先企业，致力于为全球企业提供先进的支付技术解决方案，在跨境支付、数字金融等领域拥有深厚的技术积累。
                    </div>
                </div>

                <div class="card">
                    <span class="card-icon">💳</span>
                    <h3 class="card-title">iPay Links跨境支付</h3>
                    <div class="card-content">
                        多年跨境支付行业经验，深度理解国际贸易支付需求，为中国企业出海提供专业的支付服务，积累了丰富的全球支付网络资源。
                    </div>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">5+</div>
                    <div class="stat-label">年行业经验</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">200+</div>
                    <div class="stat-label">企业客户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">40+</div>
                    <div class="stat-label">国家覆盖</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">50亿</div>
                    <div class="stat-label">美元交易额</div>
                </div>
            </div>
        </div>

        <!-- 第3页：非洲市场分析 -->
        <div class="slide fade-in">
            <div class="slide-header">
                <h2 class="slide-title">🌍 非洲支付市场机遇</h2>
                <p class="slide-subtitle">全球增长最快的数字支付市场</p>
            </div>

            <div class="africa-regions">
                <div class="region-card east">
                    <div class="region-title">🌅 东非：移动支付领先</div>
                    <div class="region-stats">
                        <div class="region-stat">
                            <div class="region-stat-value">75%</div>
                            <div class="region-stat-label">移动钱包渗透率</div>
                        </div>
                        <div class="region-stat">
                            <div class="region-stat-value">15%</div>
                            <div class="region-stat-label">现金使用</div>
                        </div>
                        <div class="region-stat">
                            <div class="region-stat-value">8%</div>
                            <div class="region-stat-label">银行转账</div>
                        </div>
                    </div>
                    <div class="region-info">
                        <strong>核心国家：</strong>肯尼亚、坦桑尼亚、乌干达、卢旺达<br>
                        <strong>支付特色：</strong>M-Pesa生态系统全球领先，移动支付普及率最高<br>
                        <strong>中非贸易：</strong>基础设施建设、制造业园区、数字经济合作
                    </div>
                </div>

                <div class="region-card west">
                    <div class="region-title">🌅 西非：银行体系发达</div>
                    <div class="region-stats">
                        <div class="region-stat">
                            <div class="region-stat-value">35%</div>
                            <div class="region-stat-label">银行转账</div>
                        </div>
                        <div class="region-stat">
                            <div class="region-stat-value">40%</div>
                            <div class="region-stat-label">现金使用</div>
                        </div>
                        <div class="region-stat">
                            <div class="region-stat-value">20%</div>
                            <div class="region-stat-label">移动钱包</div>
                        </div>
                    </div>
                    <div class="region-info">
                        <strong>核心国家：</strong>尼日利亚、加纳、塞内加尔、科特迪瓦、喀麦隆<br>
                        <strong>支付特色：</strong>传统银行体系相对发达，USSD银行服务普及<br>
                        <strong>中非贸易：</strong>石油天然气、农业、电信基础设施
                    </div>
                </div>

                <div class="region-card south">
                    <div class="region-title">🌅 南非：国际化程度高</div>
                    <div class="region-stats">
                        <div class="region-stat">
                            <div class="region-stat-value">40%</div>
                            <div class="region-stat-label">国际卡支付</div>
                        </div>
                        <div class="region-stat">
                            <div class="region-stat-value">25%</div>
                            <div class="region-stat-label">移动钱包</div>
                        </div>
                        <div class="region-stat">
                            <div class="region-stat-value">20%</div>
                            <div class="region-stat-label">现金使用</div>
                        </div>
                    </div>
                    <div class="region-info">
                        <strong>核心国家：</strong>南非、赞比亚、马拉维<br>
                        <strong>支付特色：</strong>最接近国际标准的支付环境，卡支付发达<br>
                        <strong>中非贸易：</strong>矿产资源、制造业、金融服务
                    </div>
                </div>

                <div class="region-card north">
                    <div class="region-title">🌅 北非：数字化潜力大</div>
                    <div class="region-stats">
                        <div class="region-stat">
                            <div class="region-stat-value">58%</div>
                            <div class="region-stat-label">现金使用</div>
                        </div>
                        <div class="region-stat">
                            <div class="region-stat-value">20%</div>
                            <div class="region-stat-label">国际卡</div>
                        </div>
                        <div class="region-stat">
                            <div class="region-stat-value">15%</div>
                            <div class="region-stat-label">银行转账</div>
                        </div>
                    </div>
                    <div class="region-info">
                        <strong>核心国家：</strong>埃及、摩洛哥、阿尔及利亚<br>
                        <strong>支付特色：</strong>传统支付为主，数字化转型空间巨大<br>
                        <strong>中非贸易：</strong>能源合作、基础设施、文化交流
                    </div>
                </div>
            </div>
        </div>

        <!-- 第4页：EX科技能力 -->
        <div class="slide fade-in">
            <div class="slide-header">
                <h2 class="slide-title">🚀 EX平台科技能力</h2>
                <p class="slide-subtitle">多元化科技输出，满足不同企业需求</p>
            </div>

            <div class="tech-showcase">
                <h3 style="text-align: center; color: #1e3c72; margin-bottom: 20px;">💡 三大科技输出模式</h3>
                <div class="tech-grid">
                    <div class="tech-item">
                        <div class="tech-icon">☁️</div>
                        <div class="tech-title">SaaS云服务</div>
                        <div class="tech-desc">
                            • 即开即用，快速上线<br>
                            • 按需付费，成本可控<br>
                            • 自动更新，免维护<br>
                            • 适合中小企业快速接入
                        </div>
                    </div>

                    <div class="tech-item">
                        <div class="tech-icon">�</div>
                        <div class="tech-title">本地化部署</div>
                        <div class="tech-desc">
                            • 私有云部署，数据安全<br>
                            • 定制化开发，深度集成<br>
                            • 专属技术支持<br>
                            • 适合大型企业集团
                        </div>
                    </div>

                    <div class="tech-item">
                        <div class="tech-icon">�</div>
                        <div class="tech-title">API技术输出</div>
                        <div class="tech-desc">
                            • RESTful API标准<br>
                            • SDK多语言支持<br>
                            • 完整技术文档<br>
                            • 适合有开发能力的企业
                        </div>
                    </div>
                </div>
            </div>

            <div class="content-grid">
                <div class="card">
                    <span class="card-icon">🔧</span>
                    <h3 class="card-title">技术架构优势</h3>
                    <div class="card-content">
                        • 微服务架构，高可用性<br>
                        • 分布式部署，全球加速<br>
                        • 实时监控，7×24运维<br>
                        • 弹性扩容，应对高并发
                    </div>
                </div>

                <div class="card">
                    <span class="card-icon">🛡️</span>
                    <h3 class="card-title">安全合规保障</h3>
                    <div class="card-content">
                        • ISO 27001信息安全认证<br>
                        • PCI DSS Level 1合规<br>
                        • 端到端加密传输<br>
                        • 多重身份验证机制
                    </div>
                </div>
            </div>
        </div>

        <!-- 第5页：非洲支付能力 -->
        <div class="slide fade-in">
            <div class="slide-header">
                <h2 class="slide-title">💳 非洲支付能力全覆盖</h2>
                <p class="slide-subtitle">深度整合本地支付生态，支付成功率98.5%</p>
            </div>

            <div class="content-grid four-col">
                <div class="card highlight-card">
                    <span class="card-icon">📱</span>
                    <h3 class="card-title">移动钱包网络</h3>
                    <div class="card-content">
                        覆盖12个核心国家<br>
                        支持20+移动钱包<br>
                        包括M-Pesa、MTN、Orange等
                    </div>
                </div>

                <div class="card highlight-card">
                    <span class="card-icon">🏦</span>
                    <h3 class="card-title">银行转账网络</h3>
                    <div class="card-content">
                        尼日利亚17家银行USSD<br>
                        南非完整银行体系<br>
                        加纳所有主要银行
                    </div>
                </div>

                <div class="card highlight-card">
                    <span class="card-icon">💳</span>
                    <h3 class="card-title">国际卡支付</h3>
                    <div class="card-content">
                        Visa/Mastercard/Amex<br>
                        Apple Pay/Google Pay<br>
                        本地数字钱包
                    </div>
                </div>

                <div class="card highlight-card">
                    <span class="card-icon">🔗</span>
                    <h3 class="card-title">Payment Links</h3>
                    <div class="card-content">
                        无代码支付链接<br>
                        品牌定制支持<br>
                        多场景应用
                    </div>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">12</div>
                    <div class="stat-label">核心国家覆盖</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">150+</div>
                    <div class="stat-label">本地支付方式</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">13</div>
                    <div class="stat-label">支持货币种类</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">98.5%</div>
                    <div class="stat-label">支付成功率</div>
                </div>
            </div>
        </div>

        <!-- 第6页：中非贸易解决方案 -->
        <div class="slide fade-in">
            <div class="slide-header">
                <h2 class="slide-title">🤝 中非贸易专业解决方案</h2>
                <p class="slide-subtitle">针对中非贸易场景深度定制</p>
            </div>

            <div class="content-grid">
                <div class="card">
                    <span class="card-icon">🏗️</span>
                    <h3 class="card-title">基础设施工程款结算</h3>
                    <div class="card-content">
                        • 大额分批结算支持（单笔最高1000万美元）<br>
                        • 多币种账户管理（人民币、美元、当地货币）<br>
                        • 资金安全监管（第三方托管）<br>
                        • 进度款自动释放（智能合约执行）
                    </div>
                </div>

                <div class="card">
                    <span class="card-icon">⛽</span>
                    <h3 class="card-title">资源贸易收付款</h3>
                    <div class="card-content">
                        • 大宗商品交易托管（石油、矿产、农产品）<br>
                        • 智能分账系统（多方利益分配）<br>
                        • 汇率风险管理（远期汇率锁定）<br>
                        • 合规审计追踪（反洗钱和贸易合规）
                    </div>
                </div>

                <div class="card">
                    <span class="card-icon">🏭</span>
                    <h3 class="card-title">制造业供应链金融</h3>
                    <div class="card-content">
                        • 批量付款处理（万级供应商同时结算）<br>
                        • 员工薪资发放（移动钱包直达）<br>
                        • 供应链融资（基于贸易数据信用评估）<br>
                        • 库存质押管理（数字化仓单质押）
                    </div>
                </div>

                <div class="card">
                    <span class="card-icon">🛒</span>
                    <h3 class="card-title">跨境电商收款</h3>
                    <div class="card-content">
                        • 本地化支付体验（适配各地区支付习惯）<br>
                        • 多语言支付页面（英语、法语、阿拉伯语）<br>
                        • 智能路由优化（自动选择最优通道）<br>
                        • AI风控反欺诈（实时风险识别）
                    </div>
                </div>
            </div>
        </div>

        <!-- 第7页：成功案例 -->
        <div class="slide fade-in">
            <div class="slide-header">
                <h2 class="slide-title">🏆 成功案例展示</h2>
                <p class="slide-subtitle">助力中国企业非洲业务成功</p>
            </div>

            <div class="content-grid">
                <div class="card">
                    <span class="card-icon">🚄</span>
                    <h3 class="card-title">大型基建企业项目</h3>
                    <div class="card-content">
                        <strong>客户：</strong>中国知名基建企业<br>
                        <strong>项目：</strong>非洲多个铁路、港口项目<br>
                        <strong>成果：</strong><br>
                        • 结算效率提升80%（15天→3天）<br>
                        • 成本降低65%<br>
                        • 员工薪资实时到账<br>
                        • 覆盖肯尼亚蒙内铁路等重点项目
                    </div>
                </div>

                <div class="card">
                    <span class="card-icon">🛍️</span>
                    <h3 class="card-title">跨境电商平台</h3>
                    <div class="card-content">
                        <strong>客户：</strong>专注非洲市场的电商平台<br>
                        <strong>挑战：</strong>支付成功率低，本地化体验差<br>
                        <strong>成果：</strong><br>
                        • 支付成功率提升至98.5%<br>
                        • 订单转化率提升45%<br>
                        • 业务扩展至25个非洲国家<br>
                        • 年交易额增长300%
                    </div>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number">200+</div>
                    <div class="stat-label">中国企业客户</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">80%</div>
                    <div class="stat-label">效率提升</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">65%</div>
                    <div class="stat-label">成本降低</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">300%</div>
                    <div class="stat-label">业务增长</div>
                </div>
            </div>
        </div>

        <!-- 第8页：联系我们 -->
        <div class="slide contact-slide fade-in">
            <div class="slide-header">
                <h2 class="slide-title">🤝 携手合作，共创未来</h2>
                <p class="slide-subtitle">立即开启您的非洲支付之旅</p>
            </div>

            <div class="contact-grid">
                <div class="contact-item">
                    <div class="contact-icon">📧</div>
                    <div class="contact-title">商务合作</div>
                    <div class="contact-info"><EMAIL></div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">🌐</div>
                    <div class="contact-title">官方网站</div>
                    <div class="contact-info">www.eurewax.com</div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">📱</div>
                    <div class="contact-title">24小时热线</div>
                    <div class="contact-info">+65 6000 0000</div>
                </div>
            </div>

            <div style="margin-top: 50px; padding: 30px; background: rgba(255,255,255,0.1); border-radius: 20px; backdrop-filter: blur(10px);">
                <h3 style="margin-bottom: 20px;">🎪 中非经贸博览会展台</h3>
                <p style="font-size: 18px; margin-bottom: 15px;">现场演示 • API对接指导 • 专家咨询</p>
                <p style="font-size: 16px; opacity: 0.9;">欢迎莅临展台，体验EX平台强大的非洲支付能力</p>
            </div>
        </div>
    </div>
</body>
</html>
