<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EX非洲支付解决方案 - 中非经贸博览会</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .header h1 {
            font-size: 48px;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .subtitle {
            font-size: 24px;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .header .tagline {
            font-size: 18px;
            background: rgba(255,255,255,0.2);
            padding: 10px 30px;
            border-radius: 25px;
            display: inline-block;
        }

        .section {
            background: white;
            margin: 30px 0;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .section:hover {
            transform: translateY(-5px);
        }

        .section-title {
            font-size: 28px;
            color: #667eea;
            margin-bottom: 20px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .africa-map {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 20px;
            margin: 20px 0;
        }

        .region-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 5px solid;
            transition: all 0.3s ease;
        }

        .region-card:hover {
            transform: scale(1.02);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .region-card.east { border-left-color: #4CAF50; }
        .region-card.west { border-left-color: #2196F3; }
        .region-card.south { border-left-color: #FF9800; }
        .region-card.north { border-left-color: #F44336; }

        .region-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }

        .region-stats {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
        }

        .stat {
            text-align: center;
            flex: 1;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
        }

        .stat-label {
            font-size: 12px;
            color: #666;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 20px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .feature-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .feature-desc {
            font-size: 14px;
            opacity: 0.9;
        }

        .api-showcase {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .api-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }

        .success-metrics {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 20px;
            margin: 20px 0;
        }

        .metric {
            text-align: center;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }

        .metric-number {
            font-size: 36px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 14px;
            opacity: 0.9;
        }

        .contact-section {
            background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
            color: white;
            text-align: center;
            padding: 40px;
            border-radius: 15px;
        }

        .contact-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 30px;
            margin-top: 30px;
        }

        .contact-item {
            padding: 20px;
        }

        .contact-icon {
            font-size: 36px;
            margin-bottom: 15px;
        }

        .contact-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .contact-info {
            font-size: 16px;
            opacity: 0.9;
        }

        @media (max-width: 768px) {
            .africa-map,
            .features-grid,
            .success-metrics,
            .contact-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 36px;
            }

            .header .subtitle {
                font-size: 20px;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>EX非洲支付解决方案</h1>
            <div class="subtitle">中非经贸博览会专业展示</div>
            <div class="tagline">连接中非贸易 · 赋能全球支付</div>
        </div>

        <div class="section">
            <h2 class="section-title">🌍 非洲支付市场全景</h2>
            <div class="africa-map">
                <div class="region-card east">
                    <div class="region-title">🌅 东非地区</div>
                    <div class="region-stats">
                        <div class="stat">
                            <div class="stat-value">75%</div>
                            <div class="stat-label">移动钱包</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">15%</div>
                            <div class="stat-label">现金</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">8%</div>
                            <div class="stat-label">银行</div>
                        </div>
                    </div>
                    <p><strong>主要国家：</strong>肯尼亚、埃塞俄比亚、坦桑尼亚</p>
                    <p><strong>支付特点：</strong>M-Pesa领先，移动支付全球最发达</p>
                </div>

                <div class="region-card west">
                    <div class="region-title">🌅 西非地区</div>
                    <div class="region-stats">
                        <div class="stat">
                            <div class="stat-value">40%</div>
                            <div class="stat-label">现金</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">35%</div>
                            <div class="stat-label">银行</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">20%</div>
                            <div class="stat-label">钱包</div>
                        </div>
                    </div>
                    <p><strong>主要国家：</strong>尼日利亚、加纳、科特迪瓦</p>
                    <p><strong>支付特点：</strong>银行体系发达，数字化转型加速</p>
                </div>

                <div class="region-card south">
                    <div class="region-title">🌅 南部非洲</div>
                    <div class="region-stats">
                        <div class="stat">
                            <div class="stat-value">40%</div>
                            <div class="stat-label">国际卡</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">25%</div>
                            <div class="stat-label">钱包</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">20%</div>
                            <div class="stat-label">现金</div>
                        </div>
                    </div>
                    <p><strong>主要国家：</strong>南非、安哥拉、赞比亚</p>
                    <p><strong>支付特点：</strong>最接近国际标准，卡支付发达</p>
                </div>

                <div class="region-card north">
                    <div class="region-title">🌅 北非地区</div>
                    <div class="region-stats">
                        <div class="stat">
                            <div class="stat-value">58%</div>
                            <div class="stat-label">现金</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">20%</div>
                            <div class="stat-label">国际卡</div>
                        </div>
                        <div class="stat">
                            <div class="stat-value">15%</div>
                            <div class="stat-label">银行</div>
                        </div>
                    </div>
                    <p><strong>主要国家：</strong>埃及、摩洛哥、阿尔及利亚</p>
                    <p><strong>支付特点：</strong>传统支付为主，数字化潜力巨大</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">🚀 EX核心解决方案</h2>
            <div class="features-grid">
                <div class="feature-card pulse">
                    <div class="feature-icon">💳</div>
                    <div class="feature-title">全覆盖支付接入</div>
                    <div class="feature-desc">支持40+非洲国家，150+本地支付方式，包括M-Pesa、MTN、Vodafone等主流钱包</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🏗️</div>
                    <div class="feature-title">中非贸易定制</div>
                    <div class="feature-desc">基建工程款、资源贸易、制造业供应链、跨境电商等场景专业方案</div>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">🛡️</div>
                    <div class="feature-title">安全合规保障</div>
                    <div class="feature-desc">银行级安全，符合40+国家监管要求，AI风控，99.2%欺诈检测准确率</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">⚡ API技术能力展示</h2>
            <div class="api-showcase">
                <h3>全面覆盖非洲支付生态</h3>
                <div class="api-code">
{
  "mobile_money_networks": {
    "burkina_faso": ["ORANGEMONEY", "MOBICASH"],
    "cameroon": ["MTN", "ORANGEMONEY"],
    "cote_divoire": ["MOOV", "MTN", "ORANGE", "WAVE"],
    "ghana": ["AIRTELTIGO", "MTN", "VODAFONE"],
    "kenya": ["M-PESA"],
    "rwanda": ["AIRTEL", "MTN"],
    "senegal": ["ORANGEMONEY", "WAVE"],
    "tanzania": ["AIRTEL", "HALOPESA", "TIGO", "VODACOM"],
    "uganda": ["AIRTEL", "MTN"],
    "zambia": ["AIRTEL", "MTN", "ZAMTEL"]
  },
  "digital_wallets": {
    "south_africa": ["1Voucher"],
    "egypt": ["Fawry Pay"],
    "nigeria": ["OPay", "eNaira"]
  },
  "ussd_banking": "支持尼日利亚17家主要银行",
  "international_cards": ["Visa", "Mastercard", "Amex"],
  "digital_payments": ["Apple Pay", "Google Pay"],
  "currencies": ["NGN", "KES", "GHS", "ZAR", "EGP", "XOF", "XAF",
                 "RWF", "TZS", "UGX", "ZMW", "USD", "EUR", "GBP"],
  "coverage": "40+ African countries",
  "success_rate": "98.5%"
}
                </div>
            </div>
        </div>

        <div class="section">
            <h2 class="section-title">📈 业务成果展示</h2>
            <div class="success-metrics">
                <div class="metric">
                    <div class="metric-number">200+</div>
                    <div class="metric-label">中国企业客户</div>
                </div>
                <div class="metric">
                    <div class="metric-number">12</div>
                    <div class="metric-label">非洲核心国家</div>
                </div>
                <div class="metric">
                    <div class="metric-number">50亿</div>
                    <div class="metric-label">美元年交易额</div>
                </div>
                <div class="metric">
                    <div class="metric-number">98.5%</div>
                    <div class="metric-label">支付成功率</div>
                </div>
            </div>
        </div>

        <div class="contact-section">
            <h2>🤝 联系我们</h2>
            <p>立即开启您的非洲支付之旅</p>
            <div class="contact-grid">
                <div class="contact-item">
                    <div class="contact-icon">📧</div>
                    <div class="contact-title">商务合作</div>
                    <div class="contact-info"><EMAIL></div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">🌐</div>
                    <div class="contact-title">官方网站</div>
                    <div class="contact-info">www.eurewax.com</div>
                </div>
                <div class="contact-item">
                    <div class="contact-icon">📱</div>
                    <div class="contact-title">24小时热线</div>
                    <div class="contact-info">+65 6000 0000</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
